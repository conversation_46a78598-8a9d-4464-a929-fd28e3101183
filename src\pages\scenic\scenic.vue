<template>
	<y-nav-bar id="nav-bar" backgroundColor="#7ED3DA" backBtn solid>{{
		state.scenicInfo.name
	}}</y-nav-bar>
	<!-- <scenicSwiper class="scenic-banner" :list="swiperList" /> -->
	<image class="scenic-banner" :src="bgImg" mode="aspectFill" />
	<view style="position: relative">
		<view class="sceinc" style="marginTop: 310rpx">
			<view class="sceinc-info">
				<view class="sceinc-info-bar"></view>
				<!-- 单票信息 -->
				<template v-if="routerParams.scenicId">
					<view class="sceinc-info__title">
						<y-font-weight>{{ state.scenicInfo.name }}</y-font-weight>
						<view class="sceinc-info__title__level" v-if="state.scenicInfo.grade !== '0'">
							{{ scenicGrade[state.scenicInfo.grade] }}</view>
						<y-checkbox class="sceinc-info__title__collect" @onCheck="onCheckCollect" :checked="isCollect"
							type="collect" />
					</view>

					<view class="sceinc-info__rating">
						<template v-if="comments.allNumber > 0">
							<view class="rating-score">{{ comments.scoreAll }}<text class="rating-unit">分</text></view>
							<view class="rating-count" @click="goToComments">{{ comments.allNumber }}条点评
								<view class="triangle-right"></view>
							</view>
						</template>
						<template v-else>
							<view class="rating-count">暂无点评</view>
						</template>
					</view>

					<view v-if="state.scenicInfo.businessStartTime" class="sceinc-info__time">
						{{
							`营业时间：${state.scenicInfo.businessStartTime} - ${state.scenicInfo.businessEndTime}`
						}}
					</view>
					<view class="sceinc-info__address">
						<image class="sceinc-info__address-icon" src="@/static/image/map-icon.png" mode="widthFix" />
						<view class="sceinc-info__address__info">
							<view class="sceinc-info__address__title">
								{{
									state.scenicInfo.provinceName +
									state.scenicInfo.cityName +
									state.scenicInfo.areaName +
									state.scenicInfo.address
								}}
							</view>
							<view class="sceinc-info__address__bottom">
								<view>
									<y-svg class="icon" name="phone-icon" />
									{{ state.scenicInfo.contractsPhone }}
								</view>
								<view style="display: flex; align-items: center" @click="nav">
									<y-svg style="margin-right: 0; margin-top: -8rpx" class="icon" name="navigation-icon" />
									去这里
								</view>
							</view>
						</view>
					</view>
					<view class="check-in-box">
						<view class="check-in-content">
							<image class="check-in-icon" referrerpolicy="no-referrer" src="@/static/image/check-in-icon.webp" />
							<text class="check-in-text">景点打卡，留下美好足迹</text>
						</view>
						<view v-if="!isCheckedIn" class="check-in-button" @click="handleCheckIn">
							<text class="check-in-button-text">去打卡</text>
						</view>
						<view v-else class="checked-in-status-button" @click="handleCheckIn">
							<text class="checked-in-status-button-text">已打卡</text>
						</view>
					</view>
				</template>
				<!-- 组合票信息 -->
				<template v-else>
					<view class="sceinc-info__title">
						<y-font-weight>{{ state.composeGoodsInfo.name }}</y-font-weight>
						<y-checkbox class="sceinc-info__title__collect" @onCheck="onCheckCollect" :checked="isCollect"
							type="collect" />
					</view>
					<view class="sceinc-info__label">
						{{
							state.composeGoodsInfo.labelList &&
							state.composeGoodsInfo.labelList.map(n => n.name).join(" / ")
						}}
					</view>
					<view class="sceinc-info__bottom">
						<view class="price">
							<view class="unit">¥</view>
							{{ state.composeGoodsInfo.totalPrice }}
							<view class="up">起</view>
						</view>
						<view class="buy" @tap="handleSubmitClick(state.composeGoodsInfo)">立即预订</view>
					</view>
				</template>
			</view>
			<view style="height: 20rpx;background: #f1f1f1;"></view>
			<!-- 权益景区详情页部分 -->
			<view v-if="isTravelPage" style="background: #f1f1f1;overflow: hidden;">
				<view v-if="routerParams.scenicId" class="tab-container" id="tab-container" :class="{ 'fixed': isFixed }"
					:style="{ top: isFixed ? '0px' : 'auto' }">
					<view class="tab-list">
						<view class="tab-item">
							<text :class="['tab-text', activeTab === 'ticket' ? 'tab-active' : 'tab-inactive']"
								@click="switchTab('ticket')">入园预订</text>
							<view v-if="activeTab === 'ticket'" class="tab-indicator"></view>
						</view>
						<view class="tab-item" style="margin-left: 40rpx;">
							<text :class="['tab-text', activeTab === 'introduction' ? 'tab-active' : 'tab-inactive']"
								@click="switchTab('introduction')">景区介绍</text>
							<view v-if="activeTab === 'introduction'" class="tab-indicator"></view>
						</view>
						<view class="tab-item" style="margin-left: 40rpx;">
							<text :class="['tab-text', activeTab === 'comment' ? 'tab-active' : 'tab-inactive']"
								@click="switchTab('comment')">景区点评</text>
							<view v-if="activeTab === 'comment'" class="tab-indicator"></view>
						</view>
					</view>
				</view>
				<view v-if="isFixed && routerParams.scenicId" :style="{ height: tabHeight + 'px' }"></view>
				<!-- 票列表组件 -->
				<view id="ticket-content">
					<l-ticket-list :ticketList="state.ticketList" :scenicId="routerParams.scenicId"
						:composeGoodsInfo="state.composeGoodsInfo" :showPopUp="showPopUp" :ticketRemark="state.ticketRemark"
						@booking="booking" @submit="handleSubmitClick" @popup-close="showPopUp = false"
						:disableTravelCard="disableTravelCard"></l-ticket-list>
				</view>
				<!-- 景区介绍 -->
				<view id="introduction-content" v-if="routerParams.scenicId">
					<view class="introduction-box">
						<view class="section-header">
							<view class="header-title">
								<view class="header-line"></view>
								<text class="header-text">景区介绍</text>
							</view>
						</view>
						<view v-if="state.scenicInfo.remark" class="content">
							<rich-text :nodes="state.scenicInfo.remark"></rich-text>
						</view>
						<view v-else style="background-color: #fff;">
							<y-empty>暂无权益景区</y-empty>
						</view>
					</view>

				</view>
				<!-- 用户点评列表 -->
				<view id="comment-content" v-if="routerParams.scenicId">
					<l-comment-list :scenicName="state.scenicInfo.name"></l-comment-list>
				</view>
			</view>
			<!-- 普通景区详情部分 -->
			<view v-if="!isTravelPage" style="background: #f1f1f1;overflow: hidden;">
				<view v-if="routerParams.scenicId" class="tab-container" id="tab-container" :class="{ 'fixed': isFixed }"
					:style="{ top: isFixed ? '0px' : 'auto' }">
					<view class="tab-list">
						<view class="tab-item">
							<text :class="['tab-text', activeTab === 'ticket' ? 'tab-active' : 'tab-inactive']"
								@click="switchTab('ticket')">门票预订</text>
							<view v-if="activeTab === 'ticket'" class="tab-indicator"></view>
						</view>
						<view class="tab-item" style="margin-left: 40rpx;">
							<text :class="['tab-text', activeTab === 'comment' ? 'tab-active' : 'tab-inactive']"
								@click="switchTab('comment')">用户点评</text>
							<view v-if="activeTab === 'comment'" class="tab-indicator"></view>
						</view>
					</view>
				</view>
				<view v-if="isFixed && routerParams.scenicId" :style="{ height: tabHeight + 'px' }"></view>
				<!-- 票列表组件 -->
				<view id="ticket-content">
					<l-ticket-list :ticketList="state.ticketList" :scenicId="routerParams.scenicId"
						:composeGoodsInfo="state.composeGoodsInfo" :showPopUp="showPopUp" :ticketRemark="state.ticketRemark"
						@booking="booking" @submit="handleSubmitClick" @popup-close="showPopUp = false"></l-ticket-list>
				</view>
				<!-- 用户点评列表 -->
				<view id="comment-content" v-if="routerParams.scenicId">
					<l-comment-list :scenicName="state.scenicInfo.name"></l-comment-list>
				</view>
			</view>

		</view>
	</view>
	<check-in-pop ref="checkInPopup" @getLocation="getLocation"></check-in-pop>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onBeforeMount, onUnmounted, nextTick } from "vue"
import scenicSwiper from "./component/scenicSwiper.vue"
import scenicMap from "./component/scenicMap.vue"
import lTicketList from "./component/l-ticket-list.vue"
import lCommentList from "./component/l-comment-list.vue"
import checkInPop from "./component/check-in-pop.vue"
import request from "@/utils/request.js"
import { mockCheckInRequest } from '@/mockApi/checkIn.js'
import {
	formatTime,
	objToUrlPath,
	getRoute,
	setJWeixin,
	markdownToHtml,
	getAddressByTx
} from "@/utils/tool.js"
import { goodsType, ticketType } from "@/utils/constant.js"
import { onLoad, onReady, onShow, onPageScroll } from "@dcloudio/uni-app"
import { scenicGrade } from "@/utils/constant.js"
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
import { Tool } from "@/utils/tools.ts"
import { getEnv } from "@/utils/getEnv";

const props = defineProps({
	tabList: {
		type: Array,
		default: () => []
	}
})
const isTravelPage = ref(false)
const imgHost = ref(getEnv().VITE_IMG_HOST)
const swiperList = ref("")
const bgImg = ref("")
const sourceType = ref(0)
const comments = ref({})
setJWeixin()
//轮播图
const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
	sourceType.value = Tool.getSystemSource()
})

// 页面显示时重置滚动位置到顶部
onShow(() => {
	// 重置滚动位置到页面顶部，避免浏览器返回时的滚动位置记忆
	// 使用 nextTick 确保页面渲染完成后再执行滚动
	nextTick(() => {
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 0 // 立即滚动，无动画
		})

		// 重置相关状态变量
		scrollTop = 0
		isFixed.value = false
		activeTab.value = 'ticket'
	})
})

const isCheckedIn = ref(false)
const checkInPopup = ref(null)
const disableTravelCard = ref(false)
// 定位超时定时器
let timer = null
const getLocation = () => {
	return new Promise((resolve, reject) => {
		// 设置 15 秒超时
		let isTimeout = false;
		// 清除之前的定时器
		if (timer) {
			clearTimeout(timer);
		}
		timer = setTimeout(() => {
			isTimeout = true;
			// 超时后使用景区定位
			const res = {
				city: state.scenicInfo.cityName,
				address: state.scenicInfo.name,
				latitude: state.scenicInfo.latitude,
				longitude: state.scenicInfo.longitude
			};
			resolve(res);
		}, 15000);

		uni.getLocation({
			type: 'gcj02',
			success: async function (res) {
				if (isTimeout) return; // 如果已经超时，不再处理
				clearTimeout(timer);

				console.log('uni-app 定位成功：');
				console.log('纬度：' + res.latitude);
				console.log('经度：' + res.longitude);
				try {
					const params = {
						lat: res.latitude,
						lng: res.longitude
					};

					const adData = await getAddressByTx(params);
					// res.addressInfo = adData;
					res.city = adData?.address_component?.city
					res.address = adData?.address
					console.log('获取详细地址成功：', adData);
				} catch (e) {
					console.error("解析地址失败", e)
					// 定位失败，直接取景区定位
					res.city = state.scenicInfo.cityName
					res.address = state.scenicInfo.name
					res.latitude = state.scenicInfo.latitude
					res.longitude = state.scenicInfo.longitude
				}
				resolve(res);
			},
			fail: function (err) {
				if (isTimeout) return; // 如果已经超时，不再处理
				clearTimeout(timer);

				uni.hideLoading();
				console.error('uni-app 定位失败：', err);
				checkInPopup.value.open('location');
				reject(err);
			}
		});
	});
}
// 处理打卡按钮点击
const handleCheckIn = async () => {
	// 在开发环境下不进行频繁打卡的判断
	const lastCheckInTime = uni.getStorageSync(
		`lastCheckInTime_${routerParams.scenicId}_${userData.userInfo.userId}`
	);
	if (lastCheckInTime) {
		const currentTime = new Date().getTime();
		const fiveMinutes = 5 * 60 * 1000;
		if (currentTime - lastCheckInTime < fiveMinutes) {
			uni.showToast({
				title: "打卡过于频繁，请缓一缓",
				icon: "none",
			});
			return;
		}
	}
	uni.showLoading({
		title: '定位中...'
	})
	const location = await getLocation()
	console.log(location)
	try {
		uni.showLoading({
			title: '打卡中...'
		})
		console.log('state.scenicInfo', state.scenicInfo)
		const address = state.scenicInfo.provinceName +
			state.scenicInfo.cityName +
			state.scenicInfo.areaName +
			state.scenicInfo.address
		const { data } = await request.post('/comment/addTClockIn', {
			scenicMsg: state.scenicInfo.picture?.split(',')[0],
			scenicId: routerParams.scenicId,
			name: state.scenicInfo.name,
			latitude: location.latitude,
			longitude: location.longitude,
			address,
			city: location.city,
			userId: userData.userInfo.userId,
		})
		if (data.isNear) {
			isCheckedIn.value = true
			uni.setStorageSync(
				`lastCheckInTime_${routerParams.scenicId}_${userData.userInfo.userId}`,
				new Date().getTime()
			);
			Tool.goPage.push(`/pages/scenic/checkInSuccess?id=${routerParams.scenicId}&latitude=${location.latitude}&longitude=${location.longitude}`)
		} else {
			checkInPopup.value.open('distance')
		}
	} catch (error) {
		console.log(error)
		if (checkInPopup.value) {
			checkInPopup.value.open('distance')
		}
	} finally {
		uni.hideLoading()
	}
}

//跳转第三方导航
const nav = () => {
	const {
		provinceName,
		cityName,
		areaName,
		address,
		latitude,
		longitude,
		pointName
	} = state.scenicInfo
	const addressName = provinceName + cityName + areaName + address
	jWeixin.openLocation({
		latitude: Number(latitude),
		longitude: Number(longitude),
		name: pointName,
		address: addressName
	})
}

const showPopUp = ref(false)
//预订须知
const booking = async e => {
	state.ticketRemark = markdownToHtml(e.ticketRemark)
	console.log(state.ticketRemark)
	// if (routerParams.scenicId) {
	// 	state.ticketRemark = markdownToHtml(e.ticketRemark)
	// } else {
	// 	const { code, data } = await request.get(`/simpleGoods/info/${e.goodsId}`)
	// 	state.ticketRemark = markdownToHtml(data.notice)
	// }
	showPopUp.value = !showPopUp.value
}
const state = reactive({
	composeGoodsInfo: {},
	scenicInfo: {},
	ticketList: [],
	ticketRemark: "" //须知
})
let userData = {}


// 在数据加载完后更新元素位置
watch(
	() => state.ticketList,
	() => {
		// 当票列表数据变化时，更新内容位置
		nextTick(() => {
			console.log("onReady 2")
			setTabPosition()
		})
	},
	{
		deep: true,
		immediate: true
	}
)

// 在页面准备完成后获取各元素位置
onReady(() => {
	setTabPosition()
	console.log("onReady 1")
	// 300ms 后再次获取位置，确保所有内容都已渲染
	setTimeout(() => {
		setTabPosition()
	}, 300)
})

//立即预定
const handleSubmitClick = async item => {
	if (routerParams.scenicId) {
		// 单票
		const urlQuery = {
			storeGoodsId: item.storeGoodsId,
			orderType: "single"
		}
		if (item.timeShareId) urlQuery.timeShareId = item.timeShareId
		const bookPath = `/pages/book/book?${objToUrlPath(urlQuery)}`
		Tool.goPage.push(bookPath)
		// if (await needCheck(item)) {
		// 	// 需要审核
		// 	if (await isApproval(item)) {
		// 		//审批通过

		// 	}
		// } else {
		// 	Tool.goPage.push(bookPath)
		// }
	} else {
		//组合票
		const urlQuery = {
			storeGoodsId: routerParams.storeGoodsId,
			orderType: "compose"
		}
		Tool.goPage.push(`/pages/book/book?${objToUrlPath(urlQuery)}`)
	}
}

//获取标签列表
const getLabels = async arr => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.post(`/ticketStore/goodsLabel`, arr)
		return data
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//获取组合票
const getGroup = async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		const { code, data } = await request.get(
			`/appTicket/composeGoodsDetail/${routerParams.storeGoodsId}`
		)
		//添加标签
		const labelArr = await getLabels([data.composeGoodsInfo.id])
		let labels
		if (labelArr.length > 0) {
			labels = [
				{
					name: "组合票"
				},
				...(labelArr[0] && labelArr[0].label ? labelArr[0].label : [])
			]
		} else {
			labels = [
				{
					name: "组合票"
				}
			]
		}
		if (data.composeGoodsInfo.isRealName == 1) {
			labels.unshift({
				name: "实名制"
			})
		}
		data.composeGoodsInfo.labelList = labels
		// 图片添加前缀
		bgImg.value = data.composeGoodsInfo.picUrl
			? imgHost.value + data.composeGoodsInfo.picUrl
			: "https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png"
		console.log(data.composeGoodsInfo.picUrl)
		//组合票信息
		data.composeGoodsInfo.note = markdownToHtml(data.composeGoodsInfo.note)
		state.composeGoodsInfo = data.composeGoodsInfo
		//组合票里的单票信息
		const ticketList = []
		data.goodsDetail.forEach(item => {
			//重组数据，相同景区的归到同个 list
			let hasScenic = false
			hasScenic =
				ticketList.length > 0 &&
				ticketList.every(e => {
					if (item.scenicId === e.scenicId) {
						e.list.push(item)
						return true
					} else {
						return false
					}
				})
			if (!hasScenic) {
				ticketList.push({
					scenicName: item.scenicName,
					scenicId: item.scenicId,
					list: [item]
				})
			}
		})
		state.ticketList = ticketList
		// 轮播图
		swiperList.value = data.picUrl && data.picUrl.split(",")[0]
		// 添加预订须知
		state.ticketList.forEach(item => {
			item?.list?.forEach(e => {
				request.get(`/simpleGoods/info/${e.goodsId}`).then(({ code, data }) => {
					e.ticketRemark = data.notice
				})
			})
		})
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//获取单票
const getTicket = async () => {
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		let params = {
			day: formatTime(Date.now(), "Y-M-D h:m:s"),
			scenicId: routerParams.scenicId,
			storeId: getRoute.params().storeId,
			isRecommend: !!routerParams.isRecommend //是否推荐
		}
		// 权益景区详情页，添加参数
		if (routerParams.travelGoodsId) {
			params.isQueryTravelGoods = true
			params.goodsId = routerParams.travelGoodsId

		}
		if (routerParams.disableTravelCard == '1') {
			disableTravelCard.value = true
		}
		const { data } = await request.get(`/appScenic/scenicInfo`, params)
		state.scenicInfo = data
		swiperList.value =
			state.scenicInfo.picture && state.scenicInfo.picture.split(",")[0]
		bgImg.value = state.scenicInfo.picture
			? imgHost.value + state.scenicInfo.picture.split(",")[0]
			: "https://yilvbao.cn/maintenance/deepfile/data/2022-08-19/upload_16637ad687153a8c34edc2c1400fc9c9.png"
		const toPush = n => {
			n.labels.unshift({
				name: goodsType[n.goodsType]
			})
			if (n.isRealName == 1) {
				n.labels.unshift({
					name: "实名制"
				})
			}
			return n
		}
		state.scenicInfo.remark = markdownToHtml(data.remark)
		data.tickList?.map(n => {
			let index = state.ticketList.map(e => e.ticketId).indexOf(n.ticketId)
			if (index >= 0) {
				state.ticketList[index].list.push(toPush(n))
			} else {
				state.ticketList.push({
					pcName: n.pcName,
					ticketId: n.ticketId,
					list: [toPush(n)]
				})
			}
		})
		uni.hideLoading()
	} catch (err) {
		console.log(err)
	}
}
//拨打电话
const dial = () => {
	uni.makePhoneCall({
		phoneNumber: state.scenicInfo.contractsPhone
	})
}

//收藏
const isCollect = ref(false)
const onCheckCollect = async () => {
	const params = {
		actionType: !isCollect.value ? 1 : 2, //类型：1 - 收藏，2 - 取消收藏
		info: {
			relationId: "", //relationType: 对应的 id- 景区 id、组合商品 id、权益卡 id
			relationType: "", //类型 1 - 景点，2 - 组合商品 3 - 权益卡
			storeId: getRoute.params().storeId,
			userId: userData.userInfo.userId
		}
	}
	if (routerParams.scenicId) {
		//景区
		params.info.relationId = routerParams.scenicId
		params.info.relationType = 1
	} else if (routerParams.storeGoodsId) {
		//组合票
		params.info.relationId = routerParams.storeGoodsId
		params.info.relationType = 2
	}
	const { code, data } = await request.post(`/my/favorites`, params)
	uni.showToast({
		icon: "none",
		title: `${!isCollect.value ? "收藏成功" : "取消收藏"}`
	})
	isCollect.value = !isCollect.value
}
const getCollectStatus = async () => {
	const params = {
		relationId: "",
		relationType: "",
		storeId: getRoute.params().storeId,
		userId: userData.userInfo.userId
	}
	if (routerParams.scenicId) {
		//景区
		params.relationId = routerParams.scenicId
		params.relationType = 1
	} else if (routerParams.storeGoodsId) {
		//组合票
		params.relationId = routerParams.storeGoodsId
		params.relationType = 2
	}
	const { data } = await request.get(`/my/confirmFavorites`, params)
	isCollect.value = data
}

// 跳转到评论页面
const goToComments = () => {
	Tool.goPage.push(`/pages/scenic/commentList?scenicId=${routerParams.scenicId}&scenicName=${state.scenicInfo.name}`)
}

const activeTab = ref('ticket')
const isFixed = ref(false)
const tabHeight = ref(0)
const tabOffsetTop = ref(0)
const ticketContentTop = ref(0)
const commentContentTop = ref(0)
const introductionContentTop = ref(0)
let scrollTop = 0

const setTabPosition = () => {
	if (!routerParams.scenicId) return
	setTimeout(() => {
		const query = uni.createSelectorQuery()
		query.select('#tab-container').boundingClientRect(data => {
			console.log("onReady", data)
			if (data) {
				tabHeight.value = data.height
				tabOffsetTop.value = data.top + scrollTop
				console.log("tabOffsetTop", tabOffsetTop.value)
			}
		}).exec()

		// 获取内容区域位置
		query.select('#ticket-content').boundingClientRect(data => {
			if (data) {
				ticketContentTop.value = data.top + scrollTop
				console.log("ticketContentTop", ticketContentTop.value)
			}
		}).exec()

		query.select('#introduction-content').boundingClientRect(data => {
			if (data) {
				introductionContentTop.value = data.top + scrollTop
				console.log("introductionContentTop", introductionContentTop.value)
			}
		}).exec()

		query.select('#comment-content').boundingClientRect(data => {
			if (data) {
				commentContentTop.value = data.top + scrollTop
				console.log("commentContentTop", commentContentTop.value)
			}
		}).exec()
	}, 500)
}

onPageScroll(e => {
	if (!routerParams.scenicId) return
	scrollTop = e.scrollTop
	isFixed.value = e.scrollTop >= tabOffsetTop.value

	// 根据滚动位置动态更新当前 tab
	if (e.scrollTop >= commentContentTop.value - tabHeight.value - 10) {
		activeTab.value = 'comment'
	} else if (e.scrollTop >= introductionContentTop.value - tabHeight.value - 10) {
		activeTab.value = 'introduction'
	} else if (e.scrollTop >= ticketContentTop.value - tabHeight.value - 10) {
		activeTab.value = 'ticket'
	}
})

const switchTab = (tab) => {
	activeTab.value = tab;

	// 先确保 tab 吸顶
	if (!isFixed.value) {
		uni.pageScrollTo({
			scrollTop: tabOffsetTop.value,
			duration: 100,
		});
	}

	// 滚动到对应内容区域
	setTimeout(() => {
		let targetPosition = 0;
		if (tab === 'ticket') {
			targetPosition = ticketContentTop.value - tabHeight.value;
		} else if (tab === 'introduction') {
			targetPosition = introductionContentTop.value - tabHeight.value;
		} else if (tab === 'comment') {
			targetPosition = commentContentTop.value - tabHeight.value;
		}

		uni.pageScrollTo({
			scrollTop: targetPosition,
			duration: 300,
		});
	}, isFixed.value ? 0 : 150);
}

// 获取景区评分概览
const getScenicRating = async () => {
	const params = {
		scenicId: routerParams.scenicId,
		storeId: routerParams.storeId
	}

	if (isTravelPage.value) {
		params.type = 3
	}
	const { data } = await request.post('/comment/statisticsScore', params)
	comments.value = {
		scoreAll: data.scoreAll.toFixed(1),
		allNumber: data.allNumber
	}
}

// 获取景区打卡信息
const getScenicCheckIn = async () => {
	const params = {
		scenicId: routerParams.scenicId,
		storeId: routerParams.storeId,
		userId: userData.userInfo.userId
	}
	const { data } = await request.post('/comment/canCheckIn', params)
	isCheckedIn.value = data
	console.log('获取景区打卡信息', data)

}

onBeforeMount(async () => {
	if (routerParams.travelGoodsId) {
		// 权益景区详情页
		isTravelPage.value = true
		document.title = '权益景区详情'
	}

	userData = await Tool.getUserInfo()
	if (routerParams.scenicId) {
		//单票
		await getTicket()
	} else if (routerParams.storeGoodsId) {
		//组合票
		await getGroup()
	}
	//获取收藏状态
	getCollectStatus()
	//获取景区评分概览
	getScenicRating()
	//获取景区打卡信息
	getScenicCheckIn()




})
onUnmounted(() => {
	uni.hideLoading()
	// 清理定时器
	if (timer) {
		clearTimeout(timer)
		timer = null
	}
})
</script>

<style lang="scss" scoped>
.scenic-banner {
	position: fixed;
	top: 0px;
	width: 100%;
	height: 500rpx;
	z-index: 1;
}

.group-ticket-title {
	font-size: 34rpx;
	font-weight: 500;
	color: #050505;
	padding: 30rpx 30rpx 15rpx;
	background-color: #fff;
}

.sceinc {
	min-height: 100%;
	background-color: transparent;
	z-index: 10;
	/* 确保内容显示在地图上方 */
	position: relative;

	/* 增加定位，使 z-index 生效 */
	.sceinc-info {
		background-color: #fff;
		border-top-left-radius: 32rpx;
		border-top-right-radius: 32rpx;
		padding: 32rpx 30rpx;
		overflow: hidden;
		position: relative;

		.sceinc-info-bar {
			width: 46rpx;
			height: 6rpx;
			background-color: #d8d8d8;
			border-radius: 3rpx;
			position: absolute;
			left: 50%;
			top: 10rpx;
			transform: translateX(-50%);
		}

		.sceinc-info__title {
			display: flex;
			align-items: center;
			font-size: 40rpx;

			.sceinc-info__title__level {
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 12rpx;
				background-color: #ffe7ca;
				border-radius: 3rpx;
				font-size: 22rpx;
				color: #ff772f;
				padding: 4rpx 10rpx;
			}

			.sceinc-info__title__collect {
				margin-left: auto;
			}
		}

		.sceinc-info__rating {
			display: inline-flex;
			border-radius: 4rpx;
			align-items: center;
			margin-top: 16rpx;
			margin-bottom: 6rpx;
			background: #E1F1FF;
			height: 42rpx;

			.rating-score {
				width: 84rpx;
				height: 42rpx;
				font-size: 28rpx;
				font-weight: 600;
				background: #349FFF;
				color: #fff;
				border-radius: 4rpx 0 25rpx 4rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 0;
				line-height: 1;

				.rating-unit {
					font-size: 24rpx;
					font-weight: 400;
					margin-left: 2rpx;
				}
			}

			.rating-count {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				color: #1C78E9;
				line-height: 1;
				padding: 0 10rpx;
				height: 42rpx;
				position: relative;

				.triangle-right {
					width: 0;
					height: 0;
					border-top: 10rpx solid transparent;
					border-bottom: 10rpx solid transparent;
					border-left: 12rpx solid #349FFF;
					margin-left: 8rpx;
					position: relative;
					top: 0rpx;
				}

				&::after {
					content: '';
					display: inline-block;
					margin-left: 6rpx;
					margin-top: 2rpx;
				}
			}
		}

		.sceinc-info__time {
			margin-top: 10rpx;
			font-size: 26rpx;
			font-weight: 400;
			color: #14131f;
		}

		.sceinc-info__address {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			border: 2rpx solid rgba(151, 151, 151, 0.23);
			border-radius: 12rpx;
			padding: 14rpx;

			.sceinc-info__address-icon {
				width: 194rpx;
			}

			.sceinc-info__address__info {
				margin-left: 20rpx;
				flex: 1;

				.sceinc-info__address__title {
					font-size: 26rpx;
					font-weight: 500;
					color: #14131f;
				}

				.sceinc-info__address__bottom {
					display: flex;
					align-items: center;
					margin-top: 12rpx;

					>view {
						display: flex;
						align-items: center;
						margin-right: 20rpx;
						font-size: 26rpx;
						font-weight: 400;
						color: #14131f;
						line-height: 30rpx;

						>.icon {
							width: 36rpx;
							height: 36rpx;
							margin-right: 10rpx;
						}

						&:last-child {
							margin-left: auto;
							color: var(--theme-color);

							>image {
								width: 40rpx;
								height: 40rpx;
							}
						}
					}
				}
			}
		}

		.check-in-box {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			background-color: rgba(241, 245, 253, 1);
			border-radius: 12rpx;
			width: 100%;
			margin-top: 20rpx;
			padding: 24rpx 20rpx 22rpx 20rpx;
			box-sizing: border-box;

			.check-in-content {
				display: flex;
				flex-direction: row;
				align-items: center;

				.check-in-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: 16rpx;
				}

				.check-in-text {
					color: rgba(23, 24, 26, 1);
					font-size: 28rpx;
					font-weight: normal;
					white-space: nowrap;
					line-height: 40rpx;
				}
			}

			.check-in-button {
				background-color: rgba(52, 159, 255, 1);
				border-radius: 28rpx;
				padding: 5rpx 27rpx 8rpx 32rpx;

				.check-in-button-text {
					color: rgba(255, 255, 255, 1);
					font-size: 26rpx;
					font-family: PingFangSC-Medium;
					font-weight: 500;
					white-space: nowrap;
					line-height: 37rpx;
				}
			}

			.checked-in-status-button {
				border-radius: 28rpx;
				border: 2rpx solid rgba(52, 159, 255, 1);
				padding: 3rpx 25rpx 6rpx 30rpx;

				.checked-in-status-button-text {
					overflow-wrap: break-word;
					color: rgba(52, 159, 255, 1);
					font-size: 26rpx;
					letter-spacing: 0.23rpx;
					font-family: PingFangSC-Medium;
					font-weight: 500;
					text-align: left;
					white-space: nowrap;
					line-height: 37rpx;
				}
			}
		}

		.sceinc-info__label {
			display: inline-block;
			padding: 2rpx 8rpx;
			margin: 20rpx 0 30rpx;
			border-radius: 6rpx;
			border: 1rpx solid #ff9201;
			font-size: 22rpx;
			font-weight: 400;
			color: #ff9201;
		}

		.sceinc-info__bottom {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.price {
				display: flex;
				align-items: baseline;
				font-size: 42rpx;
				font-weight: 600;
				color: #f43636;

				.unit {
					margin-right: 1rpx;
					font-size: 28rpx;
				}

				.up {
					margin-left: 4rpx;
					font-size: 28rpx;
					color: #6b6b6b;
					font-weight: 400;
				}
			}

			.buy {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10rpx 20rpx;
				background: #ff9201;
				border-radius: 34rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #ffffff;
				letter-spacing: 1rpx;
			}
		}

		.info-box,
		.info-box-group {
			padding: 32rpx 30rpx 34rpx;
		}

		.info-box-group {
			.group-title {
				display: flex;
				justify-content: space-between;
				font-size: 30rpx;
				font-weight: 400;
				color: #000000;
				line-height: 30rpx;
			}
		}
	}

	.title {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;

		.name {
			font-size: 40rpx;
			font-weight: 500;
			color: #000000;
		}

		.level {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 14rpx;
			padding: 0 12rpx;
			height: 34rpx;
			background: #ffe7ca;
			border-radius: 6rpx;
			font-size: 22rpx;
			font-weight: 400;
			color: #ff772f;
		}

		.phone-icon {
			$w: 54rpx;
			width: $w;
			height: $w;
			margin-left: auto;
		}

		.collect {
			margin-left: auto;
		}
	}

	.ticket {
		// margin-bottom: 30rpx;
		// margin-top: 42rpx;
		// border-radius: 24rpx;

		>.title {
			padding-top: 30rpx;
			margin-bottom: 15rpx;
			color: #050505;
			font-size: 32rpx;
			font-weight: 500;
		}

		.item {
			margin: 0 30rpx;
			padding: 34rpx 0;

			&:not(:last-child) {
				border-bottom: 1px solid rgba(157, 157, 157, 0.2);
			}

			&:last-child {
				border-bottom-left-radius: 24rpx;
				border-bottom-right-radius: 24rpx;
			}

			&:first-child {
				border-top-left-radius: 24rpx;
				border-top-right-radius: 24rpx;
				// background: #FF772F;
			}

			>.title {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 5rpx;

				.left {
					margin-right: 8rpx;
					font-size: 32rpx;
					font-weight: 400;
					color: #000000;
					// line-height: 32rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
				}

				.right {
					flex: none;
					font-size: 42rpx;
					font-weight: 600;
					color: #f43636;
					line-height: 42rpx;

					.unit {
						font-size: 28rpx;
					}

					.up {
						font-size: 28rpx;
						color: #6b6b6b;
					}
				}
			}

			.time {
				font-size: 26rpx;
				font-weight: 400;
				color: #050505;
			}

			.merit {
				display: inline-block;
				padding: 2rpx 8rpx;
				font-size: 22rpx;
				font-weight: 400;
				color: #ff9201;
				border: 1rpx solid #ff9201;
				border-radius: 6rpx;
				margin: 2rpx 0;
				height: 38rpx;
				line-height: 38rpx;
			}

			.reserve {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;

				.left {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #1c78e9;
					line-height: 33rpx;

					.icon {
						width: 14rpx;
						margin-left: 8rpx;
					}
				}

				.right {
					display: inline-block;
					padding: 12rpx 24rpx;
					font-size: 26rpx;
					font-weight: 500;
					color: #ffffff;
					line-height: 37rpx;
					letter-spacing: 1px;
					background: #ff9201;
					border-radius: 34rpx;
				}
			}
		}
	}

	.group-ticket {
		margin-bottom: 30rpx;
		border-radius: 24rpx;

		>.title {
			padding-top: 30rpx;
			font-weight: 600;
			margin-bottom: 20rpx;
			color: #050505;
			font-size: 34rpx;
		}

		.item {
			margin-bottom: 30rpx;
			padding: 34rpx 30rpx;
			background-color: #fff;
			border-radius: 24rpx;

			>.title {
				margin-right: 8rpx;
				font-weight: 500;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 34rpx;
				color: #14131f;
			}

			.ticket-item {
				padding-top: 30rpx;
				// padding: 24rpx 20rpx 20rpx;
				// border-radius: 8rpx;
				overflow: hidden;

				.ticket-name {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					font-weight: 400;
					color: #000000;
				}

				.ticket-info {
					display: flex;
					align-items: center;
					margin-top: 15rpx;

					.realName {
						margin-right: 28rpx;
						padding: 2rpx 12rpx;
						background: #ffe7ca;
						border-radius: 6rpx;
						font-size: 22rpx;
						font-weight: 400;
						color: #ff772f;
					}

					.ticket-number {
						font-size: 22rpx;
						font-weight: 400;
						color: #14131f;
					}
				}
			}

			.ticket-item:not(:last-child) {
				border-bottom: 1px solid rgba(157, 157, 157, 0.2);
				padding-bottom: 30rpx;
			}

			.ticket-reserve {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;
				margin-top: 15rpx;

				.left {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-weight: 400;
					color: #1c78e9;
					line-height: 33rpx;

					.icon {
						width: 14rpx;
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	.product-explain {
		margin-bottom: 30rpx;
		margin-top: 42rpx;
		border-radius: 24rpx;

		>.title {
			margin-bottom: 20rpx;
			color: #050505;
			font-size: 32rpx;
			font-weight: 500;
		}

		.note {
			margin-bottom: 30rpx;
			padding: 34rpx 30rpx;
			background-color: #fff;
			border-radius: 24rpx;
		}
	}

	.box_1 {
		background-color: rgba(255, 255, 255, 1);
		padding: 16rpx 30rpx 0 44rpx;
		margin-bottom: 20rpx;
	}

	.text-wrapper_33 {
		width: 100%;
		display: flex;
		justify-content: flex-start;
	}

	.tab-container {
		background-color: #ffffff;
		padding: 16rpx 30rpx 0 44rpx;
		// 
	}

	.tab-list {
		width: 100%;
		display: flex;
		justify-content: flex-start;
	}

	.tab-item {
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.tab-text {
		font-size: 32rpx;
		letter-spacing: 0.28rpx;
		font-family: PingFangSC-Medium;
		font-weight: 500;
		text-align: left;
		white-space: nowrap;
		line-height: 45rpx;
	}

	.tab-active {
		color: rgba(23, 24, 26, 1);
	}

	.tab-inactive {
		color: rgba(153, 153, 153, 1);
	}

	.tab-indicator {
		background-color: rgba(23, 24, 26, 1);
		border-radius: 2rpx;
		width: 56rpx;
		height: 4rpx;
		margin-top: 15rpx;
	}

	.introduction-box {
		background: #fff;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;

		.content {
			margin-top: 20rpx;
			font-size: 28rpx;
			line-height: 1.6;
			color: #666;
		}
	}

	.section-header {
		.header-title {
			display: flex;
			flex-direction: row;
			align-items: center;
			margin: 6rpx 0 8rpx 0;

			.header-line {
				background-color: #4787FB;
				border-radius: 3rpx;
				width: 8rpx;
				height: 36rpx;
				margin-right: 10rpx;
			}

			.header-text {
				color: #14131F;
				font-size: 36rpx;
				font-weight: 500;
				line-height: 36rpx;
				white-space: nowrap;
			}
		}
	}

	.tab-container.fixed {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 100;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
}
</style>
