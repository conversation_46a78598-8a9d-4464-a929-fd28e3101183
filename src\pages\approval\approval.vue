<template>
	<y-nav-bar backgroundColor="#7ED3DA" backBtn solid>{{ "审批" }}</y-nav-bar>
	<!-- 提交审批 -->
	<template v-if="isEdit">
		<view class="approval">
			<view class="panel">
				<view class="app-title">审批内容</view>
				<view class="sp-content">{{ state.title || '暂无审批内容' }}</view>
			</view>
			<view class="panel">
				<view class="app-title">说明信息</view>

				<view class="app-textarea">
					<textarea class="textarea" v-model="approvalDetail.notice" placeholder-class="textarea-placeholder-class"
						:maxlength="100" placeholder="请输入不超过100字" />
					<view class="word-count">{{ approvalDetail.notice ? approvalDetail.notice.length : 0 }}/100</view>
				</view>

				<view class="divider" />
				<view class="img-title">
					图片信息
					<text class="tip">(最多可上传 5 张 .jpg .png .jpeg)</text>
				</view>
				<view class="img-list">
					<y-upload upload v-model="approvalDetail.picture" :limit="5" />
				</view>
			</view>
			<!-- <y-button :disable='disable' @tap="approval">{{ routerParams.edit ? '重新提交' : '提交' }}</y-button> -->
			<y-button :disable="disable" @tap="approval">{{ "提交" }}</y-button>
		</view>
	</template>
	<!-- 审批详情 -->
	<template v-else>
		<view class="approval-detail">
			<view class="approval-status">
				<view class="title">
					{{
						approvalDetail.status == 1
							? "审批驳回"
							: approvalDetail.status == 0
								? "审批中"
								: ""
					}}
				</view>
				<view class="reason" v-if="approvalDetail.status == 1">驳回原因:{{ approvalDetail.reason }}</view>
			</view>
			<view class="approval-content">
				<view class="title">
					<y-font-weight>审核内容</y-font-weight>
				</view>
				<view class="content">{{ approvalDetail.notice }}</view>
				<template v-if="approvalDetail.picture">
					<view class="title">
						<y-font-weight>图片信息</y-font-weight>
					</view>
					<view class="imgList">
						<y-upload v-model="approvalDetail.picture" />
					</view>
				</template>
			</view>
		</view>
		<y-button :disable="false" @tap="isEdit = true">编辑</y-button>
	</template>
</template>
<script setup>
import { toRefs, reactive, ref, watch, onBeforeMount, onMounted } from "vue"
import request from "@/utils/request.js"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { getEnv } from "@/utils/getEnv";

// 如果需要定义 props，可以使用 defineProps<{属性名：类型}>()
const routerParams = reactive({})
onLoad(option => {
	for (let key in option) {
		routerParams[key] = option[key]
	}
})
const isEdit = ref(false)
const disable = ref(true)
const state = reactive({
	Autonym: {},
	title: ""
})
const approvalDetail = ref({
	picture: ""
})

watch(
	() => approvalDetail.value.notice,
	value => {
		if (value != "") {
			disable.value = false
		} else {
			disable.value = true
		}
	}
)
let userData = {}
onMounted(async () => {
	init()
})
//初始化
const init = async () => {
	console.log("init")
	try {
		uni.showLoading({
			title: "易旅宝",
			mask: true
		})
		userData = await Tool.getUserInfo()
		state.Autonym = userData.realNameInfo

		const { productId, typeProduct, unitId } = routerParams
		const params = {
			productId, // 权益卡 id / 门票产品 id
			idCard: state.Autonym.idNumber, // 身份证 id
			typeProduct, //	1:门票 / 2：权益卡
			unitId //当前购买的 门票商品 id / 权益卡商品 id
		}
		// 获取审核状态
		request.get(`/examineTravelGoods/approval`, params).then(res => {
			approvalDetail.value = res.data
			if (res.data.status || res.data.status === 0) {
				// 已提交审核
				isEdit.value = false
			} else {
				// 未提交审核
				isEdit.value = true
			}
		})
		// 获取审核要求
		request.put(`/ticketIssue/issueCheck`, params).then(res => {
			state.title = res.data.content
		})
	} catch (err) {
		console.log(err)
	}
	uni.hideLoading()
}
//审核 提交
const approval = async () => {
	if (!approvalDetail.value.notice) {
		uni.showToast({
			icon: "error",
			title: "请填写审批内容"
		})
		return
	}
	try {
		const params = {
			enclosure: "", // 附件
			idCard: state.Autonym.idNumber, // 身份证 id
			notice: approvalDetail.value.notice, // 文本信息
			phone: userData.userInfo.phone,
			picture: approvalDetail.value.picture,
			typeProduct: routerParams.typeProduct, //1:门票 / 2：权益卡
			unitId: routerParams.unitId, //当前购买的 门票商品 id / 权益卡商品 id
			productId: routerParams.productId, //权益卡 id / 门票产品 id
			url: getEnv().VITE_MALL_HOST +
				`/#/pages/book/book?storeGoodsId=${routerParams.storeGoodsId}&orderType=travel&storeId=${routerParams.storeId}`, // 购买连接
			userName: state.Autonym.idName
		}
		console.log(approvalDetail.value.status)
		if (approvalDetail.value.status === 0) {
			// 审核中，覆盖记录
			params.id = approvalDetail.value.id
			await request.put(`/examineTravelGoods/info`, params)
		} else {
			// 其他状态，添加一条审核记录
			await request.post(`/examineTravelGoods/info`, params)
		}
		Tool.goPage.back()
	} catch (err) {
		console.log(err)
	}
}
</script>
<style lang="scss" scoped>
.approval {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #F1F1F1;
	min-height: 100%;

	.panel {
		margin: 30rpx;
		padding: 20rpx 40rpx;
		background-color: #fff;
		border-radius: 12rpx;

		.app-title {
			margin-bottom: 26rpx;
			font-size: 32rpx;
			font-weight: 400;
			color: #000000;
			line-height: 40rpx;

			text {
				color: #f43636;
			}
		}

		.sp-content {
			color: #999;
			margin-bottom: 30rpx;
			font-size: 28rpx;
		}

		.app-textarea {
			position: relative;

			.textarea {
				width: 100%;
				height: 270rpx;
				// margin-bottom: 30rpx;
				// padding: 20rpx;
				// background-color: #f5f5f5;
				border-radius: 16rpx;
			}

			.word-count {
				position: absolute;
				right: 20rpx;
				bottom: 20rpx;
				font-size: 24rpx;
				color: #C5C5C5;
			}
		}

		.divider {
			margin: 40rpx 0;
			border-bottom: 1rpx dashed #EAEAEA;
		}
	}

	.img-title {
		margin-bottom: 20rpx;
		font-size: 34rpx;
		font-weight: 500;
		color: #000000;
		line-height: 48rpx;

		.tip {
			font-size: 24rpx;
			font-weight: 400;
			color: #c5c5c5;
			line-height: 33rpx;
		}
	}

	.img-list {}
}

.approval-detail {
	.approval-status {
		background: linear-gradient(146deg, #31c7d3 0%, #b5ebef 100%, #b5ebef 100%);
		padding: 30rpx 40rpx;
		color: #ffffff;
		height: 164rpx;
		display: flex;
		flex-direction: column;
		justify-content: end;

		.title {
			font-size: 48rpx;
			font-weight: 600;
			color: #ffffff;
			line-height: 67rpx;
		}

		.reason {
			font-size: 26rpx;
			margin-top: 15rpx;
		}
	}

	.approval-content {
		padding: 30rpx 40rpx;
		background-color: #fff;

		>.title {
			margin-bottom: 15rpx;
			font-size: 34rpx;
			font-weight: 500;
			color: #000000;
			line-height: 48rpx;
		}

		>.content {
			margin-bottom: 50rpx;
			font-size: 28rpx;
			font-weight: 400;
			color: #050505;
			line-height: 40rpx;
		}

		image {
			width: 145rpx;
			height: 145rpx;
			border-radius: 16rpx;
		}

		.imgList {
			.img {
				width: 145rpx;
				height: 145rpx;
				border-radius: 16rpx;
			}
		}
	}
}
</style>
